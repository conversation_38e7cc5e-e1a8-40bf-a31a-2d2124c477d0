import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/deposit/usdt_channel.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class UsdtWithdrawState extends Equatable {
  final List<USDTChannel> usdtChannels;
  final USDTChannel? selectedChannel;
  final bool isLoadingChannels;
  final String? selectedAmount;
  final String? withdrawalAmount;
  final List<USDTWallet> usdtWalletAddresses;
  final bool isLoadingWalletAddresses;
  final USDTWallet? selectedWalletAddress;
  final DataStatus submitStatus;

  const UsdtWithdrawState({
    this.usdtChannels = const [],
    this.selectedChannel,
    this.isLoadingChannels = false,
    this.selectedAmount,
    this.withdrawalAmount,
    this.usdtWalletAddresses = const [],
    this.isLoadingWalletAddresses = false,
    this.selectedWalletAddress,
    this.submitStatus = DataStatus.idle,
  });

  UsdtWithdrawState copyWith({
    List<USDTChannel>? usdtChannels,
    USDTChannel? selectedChannel,
    bool? isLoadingChannels,
    String? Function()? selectedAmount,
    String? Function()? withdrawalAmount,
    List<USDTWallet>? usdtWalletAddresses,
    bool? isLoadingWalletAddresses,
    USDTWallet? selectedWalletAddress,
    DataStatus? submitStatus,
  }) {
    return UsdtWithdrawState(
      usdtChannels: usdtChannels ?? this.usdtChannels,
      selectedChannel: selectedChannel ?? this.selectedChannel,
      isLoadingChannels: isLoadingChannels ?? this.isLoadingChannels,
      selectedAmount: selectedAmount != null ? selectedAmount() : this.selectedAmount,
      withdrawalAmount: withdrawalAmount != null ? withdrawalAmount() : this.withdrawalAmount,
      usdtWalletAddresses: usdtWalletAddresses ?? this.usdtWalletAddresses,
      isLoadingWalletAddresses: isLoadingWalletAddresses ?? this.isLoadingWalletAddresses,
      selectedWalletAddress: selectedWalletAddress ?? this.selectedWalletAddress,
      submitStatus: submitStatus ?? this.submitStatus,
    );
  }

  @override
  List<Object?> get props => [
        usdtChannels,
        selectedChannel,
        isLoadingChannels,
        selectedAmount,
        withdrawalAmount,
        usdtWalletAddresses,
        isLoadingWalletAddresses,
        selectedWalletAddress,
        submitStatus,
      ];
}
